package controllers

import (
	"textile-factory-backend/config"
	"textile-factory-backend/models"
	"textile-factory-backend/response"

	"github.com/gin-gonic/gin"
)

func ScanQRCode(c *gin.Context) {
	qrCode := c.Param("qr_code")

	var machine models.Machine
	result := config.DB.Where("machine_code = ?", qrCode).First(&machine)

	if result.Error != nil {
		response.ErrorWithData(c, response.NOT_FOUND, "二维码无效或机器不存在", gin.H{"code": "MACHINE_NOT_FOUND"})
		return
	}

	// 获取机器的当前异常数量
	var anomalyCount int64
	config.DB.Model(&models.Anomaly{}).
		Where("machine_code = ? AND alarm_type != '已完成'", machine.MachineCode).
		Count(&anomalyCount)

	// 获取最新的异常记录
	var latestAnomaly models.Anomaly
	config.DB.Where("machine_code = ?", machine.MachineCode).
		Order("state_time DESC").
		First(&latestAnomaly)

	response_data := gin.H{
		"machine":       machine,
		"anomaly_count": anomalyCount,
	}

	if latestAnomaly.MachineStateAlarmID > 0 {
		response_data["latest_anomaly"] = latestAnomaly
	}

	response.Success(c, response_data)
}

func ScanForReport(c *gin.Context) {
	qrCode := c.Param("qr_code")

	var machine models.Machine
	result := config.DB.Where("machine_code = ?", qrCode).First(&machine)

	if result.Error != nil {
		response.ErrorWithData(c, response.NOT_FOUND, "二维码无效或机器不存在", gin.H{"code": "MACHINE_NOT_FOUND"})
		return
	}

	// 检查是否有未完成的异常
	var pendingAnomaly models.Anomaly
	err := config.DB.Where("machine_code = ? AND alarm_type IN ('待维修', '维修中')", machine.MachineCode).
		Order("state_time DESC").
		First(&pendingAnomaly).Error

	response_data := gin.H{
		"machine":    machine,
		"can_report": true,
		"message":    "可以上报异常",
	}

	if err == nil {
		response_data["can_report"] = false
		response_data["message"] = "该机器已有未完成的异常报告"
		response_data["pending_anomaly"] = pendingAnomaly
	}

	response.Success(c, response_data)
}

func ScanForRepair(c *gin.Context) {
	qrCode := c.Param("qr_code")

	var machine models.Machine
	result := config.DB.Where("machine_code = ?", qrCode).First(&machine)

	if result.Error != nil {
		response.ErrorWithData(c, response.NOT_FOUND, "二维码无效或机器不存在", gin.H{"code": "MACHINE_NOT_FOUND"})
		return
	}

	// 查找待维修的异常
	var pendingAnomalies []models.Anomaly
	config.DB.Where("machine_id = ? AND status = '待维修'", machine.MachineId).
		Preload("User").
		Order("created_at ASC").
		Find(&pendingAnomalies)

	// 查找维修中的异常
	var repairingAnomalies []models.Anomaly
	config.DB.Where("machine_id = ? AND status = '维修中'", machine.MachineId).
		Preload("User").
		Order("created_at ASC").
		Find(&repairingAnomalies)

	response_data := gin.H{
		"machine":             machine,
		"pending_anomalies":   pendingAnomalies,
		"repairing_anomalies": repairingAnomalies,
	}

	if len(pendingAnomalies) == 0 && len(repairingAnomalies) == 0 {
		response_data["message"] = "该机器暂无待维修或维修中的异常"
	} else {
		response_data["message"] = "发现异常记录，可以进行维修操作"
	}

	response.Success(c, response_data)
}
