package routes

import (
	"textile-factory-backend/controllers"
	"textile-factory-backend/middleware"
	"github.com/gin-gonic/gin"
)

func SetupRoutes(r *gin.Engine) {
	api := r.Group("/api/v1")
	
	// 公开接口
	api.POST("/login", controllers.Login)
	api.POST("/refresh-token", controllers.RefreshToken)
	
	// 需要认证的接口
	auth := api.Group("/")
	auth.Use(middleware.AuthMiddleware())
	{
		// 用户信息
		auth.GET("/user/info", controllers.GetUserInfo)
		
		// 二维码扫描相关
		qr := auth.Group("/qr")
		{
			qr.GET("/scan/:qr_code", controllers.ScanQRCode)
			qr.GET("/scan/report/:qr_code", controllers.ScanForReport)
			qr.GET("/scan/repair/:qr_code", controllers.ScanForRepair)
		}
		
		// 机器管理
		machine := auth.Group("/machines")
		{
			machine.GET("/", controllers.GetMachineList)
			machine.GET("/code/:code", controllers.GetMachineByCode)
			machine.GET("/qr/:qr_code", controllers.GetMachineByQRCode)
			machine.PUT("/:id/status", controllers.UpdateMachineStatus)
		}
		
		// 异常管理
		anomaly := auth.Group("/anomalies")
		{
			anomaly.POST("/", controllers.CreateAnomaly)
			anomaly.GET("/", controllers.GetAnomalyList)
			anomaly.GET("/:id", controllers.GetAnomalyDetail)
			anomaly.PUT("/:id/status", controllers.UpdateAnomalyStatus)
		}
		
		// 维修管理 - 仅机修工可访问 (workerType = 1)
		repair := auth.Group("/repairs")
		repair.Use(middleware.WorkerTypeMiddleware(1)) // 机修工 workerType = 1
		{
			repair.POST("/start", controllers.StartRepair)
			repair.PUT("/:id/complete", controllers.CompleteRepair)
			repair.GET("/", controllers.GetRepairList)
			repair.GET("/:id", controllers.GetRepairDetail)
		}
	}
}
