package config

import (
	"log"
	"os"
	"textile-factory-backend/models"
	"textile-factory-backend/utils"
	"time"

	"gorm.io/driver/sqlserver"
	"gorm.io/gorm"
)

var DB *gorm.DB

func InitDB() error {
	dsn := os.Getenv("DB_DSN")
	if dsn == "" {
		// 使用本地SQL Server配置，用户名密码认证
		dsn = "sqlserver://sa:123456@localhost:1433?database=textileDB&connection+timeout=30"
	}

	log.Printf("Attempting to connect to database with DSN: %s", dsn)

	var err error
	DB, err = gorm.Open(sqlserver.Open(dsn), &gorm.Config{})
	if err != nil {
		log.Printf("Failed to connect to database: %v", err)
		return err
	}

	// 配置连接池
	sqlDB, err := DB.DB()
	if err != nil {
		log.Printf("Failed to get database instance: %v", err)
		return err
	}

	// 设置连接池参数
	sqlDB.SetMaxIdleConns(10)
	sqlDB.SetMaxOpenConns(100)
	sqlDB.SetConnMaxLifetime(time.Hour)

	log.Println("Database connection established successfully")

	// 自动迁移数据库表
	log.Println("Starting database migration...")
	err = DB.AutoMigrate(
		&models.WorkerInfo{},
		&models.Machine{},
		&models.Anomaly{},
		&models.Repair{},
		&models.RefreshToken{},
	)
	if err != nil {
		log.Printf("Database migration failed: %v", err)
		return err
	}

	log.Println("Database migration completed successfully")

	// 创建默认管理员用户
	log.Println("Creating default users and machines...")
	createDefaultUsers()
	createDefaultMachines()
	log.Println("Default data creation completed")

	return nil
}

func createDefaultUsers() {
	// 生成默认密码123456的MD5哈希
	defaultPasswordMD5 := utils.HashPasswordMD5("123456")

	// 创建或更新默认织工用户
	var weaver models.User
	result := DB.Where("WorkerName = ?", "weaver01").First(&weaver)
	if result.Error == gorm.ErrRecordNotFound {
		weaver = models.User{
			WorkerName: "weaver01",
			Password:   defaultPasswordMD5,
			WorkClass:  "织工",
			WorkerType: 0, // 织工 workerType = 0
			Dept:       "生产部",
			Sex:        "男",
		}
		DB.Create(&weaver)
		log.Printf("Created default weaver user: weaver01")
	} else {
		// 更新现有用户的密码和workerType
		DB.Model(&weaver).Updates(map[string]interface{}{
			"Password":   defaultPasswordMD5,
			"WorkerType": 0, // 织工 workerType = 0
		})
		log.Printf("Updated weaver01 password and workerType")
	}

	// 创建或更新默认机修工用户
	var mechanic models.User
	result = DB.Where("WorkerName = ?", "mechanic01").First(&mechanic)
	if result.Error == gorm.ErrRecordNotFound {
		mechanic = models.User{
			WorkerName: "mechanic01",
			Password:   defaultPasswordMD5,
			WorkClass:  "机修工",
			WorkerType: 1, // 机修工 workerType = 1
			Dept:       "维修部",
			Sex:        "男",
		}
		DB.Create(&mechanic)
		log.Printf("Created default mechanic user: mechanic01")
	} else {
		// 更新现有用户的密码和workerType
		DB.Model(&mechanic).Updates(map[string]interface{}{
			"Password":   defaultPasswordMD5,
			"WorkerType": 1, // 机修工 workerType = 1
		})
		log.Printf("Updated mechanic01 password and workerType")
	}
}

func createDefaultMachines() {
	machines := []models.Machine{
		{MachineCode: "M001", MachineName: "织机001"},
		{MachineCode: "M002", MachineName: "织机002"},
		{MachineCode: "M003", MachineName: "织机003"},
		{MachineCode: "M004", MachineName: "织机004"},
		{MachineCode: "M005", MachineName: "织机005"},
	}

	for _, machine := range machines {
		var existing models.Machine
		result := DB.Where("machine_code = ?", machine.MachineCode).First(&existing)
		if result.Error == gorm.ErrRecordNotFound {
			DB.Create(&machine)
		}
	}
}
