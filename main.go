package main

import (
	"log"
	"textile-factory-backend/config"
	"textile-factory-backend/routes"
	"github.com/gin-gonic/gin"
	"github.com/gin-contrib/cors"
)

func main() {
	// 初始化数据库
	if err := config.InitDB(); err != nil {
		log.Fatal("Failed to connect to database:", err)
	}
	
	// 设置Gin模式
	gin.SetMode(gin.ReleaseMode)
	
	// 创建Gin实例
	r := gin.Default()
	
	// 配置CORS
	corsConfig := cors.DefaultConfig()
	corsConfig.AllowAllOrigins = true
	corsConfig.AllowHeaders = []string{"Origin", "Content-Length", "Content-Type", "Authorization"}
	corsConfig.AllowMethods = []string{"GET", "POST", "PUT", "DELETE", "OPTIONS"}
	r.Use(cors.New(corsConfig))
	
	// 设置路由
	routes.SetupRoutes(r)
	
	// 健康检查端点
	r.GET("/health", func(c *gin.Context) {
		c.<PERSON>(200, gin.H{
			"status": "ok",
			"service": "textile-factory-backend",
		})
	})
	
	// 启动服务器
	log.Println("Server starting on :8080")
	if err := r.Run(":8080"); err != nil {
		log.Fatal("Failed to start server:", err)
	}
}