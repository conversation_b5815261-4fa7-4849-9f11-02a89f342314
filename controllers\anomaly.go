package controllers

import (
	"strconv"
	"time"
	"textile-factory-backend/config"
	"textile-factory-backend/models"
	"textile-factory-backend/constants"
	"textile-factory-backend/response"
	"github.com/gin-gonic/gin"
)

type AnomalyRequest struct {
	MachineCode string `json:"machine_code" binding:"required"`
	AlarmType   string `json:"alarm_type" binding:"required"`
	AlarmRmark  string `json:"alarm_rmark"`
	StateA      string `json:"state_a" binding:"required"`
	StateM      string `json:"state_m" binding:"required"`
}

func CreateAnomaly(c *gin.Context) {
	var req AnomalyRequest
	if err := c.ShouldBindJSON(&req); err != nil {
		response.BadRequest(c, err.Error())
		return
	}
	
	userID, _ := c.Get("user_id")
	
	// 验证机器是否存在
	var machine models.Machine
	if err := config.DB.Where("machine_code = ?", req.MachineCode).First(&machine).Error; err != nil {
		response.NotFound(c, "机器不存在")
		return
	}
	
	// 获取用户信息用于WorkerName
	var user models.User
	config.DB.First(&user, userID)
	
	anomaly := models.Anomaly{
		MachineCode: req.MachineCode,
		Machine:     machine.MachineName,
		StateM:      req.StateM,
		StateA:      req.StateA,
		StateTime:   time.Now(),
		EndTime:     time.Now(),
		AlarmRmark:  req.AlarmRmark,
		AlarmType:   req.AlarmType,
		UpdateDtm:   time.Now(),
		UpdateBy:    user.WorkerName,
		WorkerName:  user.WorkerName,
		MSit:        machine.MSit,
		FSit:        machine.FSit,
		DayChar:     time.Now().Format("2006-01-02"),
		F:           machine.F,
		D:           time.Now().Format("2006-01-02"),
		SPT:         0,
		SPTStr:      "0",
	}
	
	if err := config.DB.Create(&anomaly).Error; err != nil {
		response.InternalServerError(c, "创建异常记录失败")
		return
	}
	
	// 更新机器状态为异常
	config.DB.Model(&machine).Update("alarm_type", req.AlarmType)
	
	// 返回完整的异常记录
	config.DB.First(&anomaly, anomaly.MachineStateAlarmID)
	
	response.SuccessWithMessage(c, "异常记录创建成功", anomaly)
}

func GetAnomalyList(c *gin.Context) {
	var anomalies []models.Anomaly
	
	page, _ := strconv.Atoi(c.DefaultQuery("page", "1"))
	pageSize, _ := strconv.Atoi(c.DefaultQuery("page_size", "10"))
	alarmType := c.Query("alarm_type")
	machineCode := c.Query("machine_code")
	
	query := config.DB.Model(&models.Anomaly{})
	
	if alarmType != "" {
		query = query.Where("alarm_type = ?", alarmType)
	}
	
	if machineCode != "" {
		query = query.Where("machine_code = ?", machineCode)
	}
	
	// 织工只能查看自己上报的异常
	workerType, _ := c.Get("worker_type")
	if workerType.(int) == 0 { // 织工 workerType = 0
		userID, _ := c.Get("user_id")
		var user models.User
		config.DB.First(&user, userID)
		query = query.Where("worker_name = ?", user.WorkerName)
	}
	
	var total int64
	query.Count(&total)
	
	offset := (page - 1) * pageSize
	result := query.Offset(offset).Limit(pageSize).Order("state_time DESC").Find(&anomalies)
	
	if result.Error != nil {
		response.InternalServerError(c, "查询异常列表失败")
		return
	}
	
	response.Pagination(c, anomalies, total, page, pageSize)
}

func GetAnomalyDetail(c *gin.Context) {
	id := c.Param("id")
	
	var anomaly models.Anomaly
	result := config.DB.Where("machine_state_alarm_id = ?", id).First(&anomaly)
	
	if result.Error != nil {
		response.NotFound(c, "异常记录不存在")
		return
	}
	
	// 织工只能查看自己的异常记录
	workerType, _ := c.Get("worker_type")
	userID, _ := c.Get("user_id")
	if workerType.(int) == 0 { // 织工 workerType = 0
		var user models.User
		config.DB.First(&user, userID)
		if anomaly.WorkerName != user.WorkerName {
			response.Forbidden(c, "没有权限查看此记录")
			return
		}
	}
	
	// 获取维修记录
	var repair models.Repair
	config.DB.Where("anomaly_id = ?", anomaly.MachineStateAlarmID).
		Preload("Mechanic").
		First(&repair)
	
	result_data := gin.H{
		"anomaly": anomaly,
	}
	
	if repair.ID > 0 {
		result_data["repair"] = repair
	}
	
	response.Success(c, result_data)
}

func UpdateAnomalyStatus(c *gin.Context) {
	id := c.Param("id")
	
	var req struct {
		AlarmType string `json:"alarm_type" binding:"required"`
	}
	
	if err := c.ShouldBindJSON(&req); err != nil {
		response.BadRequest(c, err.Error())
		return
	}
	
	result := config.DB.Model(&models.Anomaly{}).Where("machine_state_alarm_id = ?", id).Update("alarm_type", req.AlarmType)
	
	if result.Error != nil {
		response.InternalServerError(c, "更新异常状态失败")
		return
	}
	
	if result.RowsAffected == 0 {
		response.NotFound(c, "异常记录不存在")
		return
	}
	
	response.SuccessWithMessage(c, "异常状态更新成功", nil)
}