# Git 清理指南

## 概述
这个文档说明如何清理已经被错误提交到版本控制中的文件。

## 需要清理的文件

根据新创建的 `.gitignore` 文件，以下文件应该从版本控制中移除：

### Go 安装文件（不应该在项目中）
- `go1.21.0.linux-amd64.tar.gz`
- `go1.21.5.linux-amd64.tar.gz`
- `go/` 目录（整个 Go 安装目录）

## 清理步骤

### 1. 从 Git 中移除文件但保留本地文件
```bash
# 进入后端项目目录
cd textile-factory-backend

# 移除 Go 安装文件
git rm --cached go1.21.0.linux-amd64.tar.gz
git rm --cached go1.21.5.linux-amd64.tar.gz

# 移除整个 go 目录
git rm -r --cached go/
```

### 2. 提交更改
```bash
git add .gitignore
git commit -m "Add .gitignore and remove Go installation files from version control"
```

### 3. 清理本地文件（可选）
如果你不需要这些文件，可以删除它们：
```bash
# 删除 Go 安装包
rm go1.21.0.linux-amd64.tar.gz
rm go1.21.5.linux-amd64.tar.gz

# 删除 Go 安装目录（如果你有系统级的 Go 安装）
rm -rf go/
```

## 注意事项

1. **Go 安装**：确保你的系统已经正确安装了 Go，不要依赖项目目录中的 Go 安装
2. **环境变量**：确保 `GOPATH` 和 `GOROOT` 环境变量正确设置
3. **依赖管理**：使用 `go mod` 来管理项目依赖，不要提交 `vendor/` 目录

## 验证清理结果

运行以下命令验证清理是否成功：
```bash
# 检查 Git 状态
git status

# 确认 .gitignore 生效
git check-ignore go1.21.0.linux-amd64.tar.gz
git check-ignore go/
```

## 项目结构

清理后，你的项目结构应该是：
```
textile-factory-backend/
├── .gitignore
├── README.md
├── main.go
├── go.mod
├── go.sum
├── config/
├── controllers/
├── middleware/
├── models/
├── routes/
└── utils/
```

## 开发环境设置

确保你的开发环境正确设置：

1. **安装 Go**：从官方网站下载并安装 Go
2. **设置环境变量**：
   ```bash
   export GOROOT=/usr/local/go  # Go 安装路径
   export GOPATH=$HOME/go       # Go 工作空间
   export PATH=$PATH:$GOROOT/bin:$GOPATH/bin
   ```
3. **验证安装**：
   ```bash
   go version
   ```

## 运行项目

```bash
# 安装依赖
go mod tidy

# 运行项目
go run main.go
```
