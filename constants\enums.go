package constants

// 用户角色枚举
type UserRole string

const (
	UserRoleWeaver   UserRole = "weaver"   // 织工
	UserRoleMechanic UserRole = "mechanic" // 机修工
)

// 用户角色中文显示映射
var UserRoleDisplayNames = map[UserRole]string{
	UserRoleWeaver:   "织工",
	UserRoleMechanic: "机修工",
}

// 用户角色ID映射 (workerType)
var UserRoleIDs = map[UserRole]int{
	UserRoleWeaver:   0, // 织工 workerType = 0
	UserRoleMechanic: 1, // 机修工 workerType = 1
}

// 根据ID获取角色 (workerType)
var UserRoleByID = map[int]UserRole{
	0: UserRoleWeaver,   // 织工 workerType = 0
	1: UserRoleMechanic, // 机修工 workerType = 1
}

// 机器状态枚举
type MachineStatus string

const (
	MachineStatusNormal     MachineStatus = "normal"     // 正常
	MachineStatusAbnormal   MachineStatus = "abnormal"   // 异常
	MachineStatusRepairing  MachineStatus = "repairing"  // 维修中
)

// 机器状态中文显示映射
var MachineStatusDisplayNames = map[MachineStatus]string{
	MachineStatusNormal:    "正常",
	MachineStatusAbnormal:  "异常",
	MachineStatusRepairing: "维修中",
}

// 异常严重程度枚举
type AnomalySeverity string

const (
	AnomalySeverityLow    AnomalySeverity = "low"    // 低
	AnomalySeverityMedium AnomalySeverity = "medium" // 中
	AnomalySeverityHigh   AnomalySeverity = "high"   // 高
)

// 异常严重程度中文显示映射
var AnomalySeverityDisplayNames = map[AnomalySeverity]string{
	AnomalySeverityLow:    "低",
	AnomalySeverityMedium: "中",
	AnomalySeverityHigh:   "高",
}

// 异常状态枚举
type AnomalyStatus string

const (
	AnomalyStatusPending    AnomalyStatus = "pending"    // 待维修
	AnomalyStatusRepairing  AnomalyStatus = "repairing"  // 维修中
	AnomalyStatusCompleted  AnomalyStatus = "completed"  // 已完成
)

// 异常状态中文显示映射
var AnomalyStatusDisplayNames = map[AnomalyStatus]string{
	AnomalyStatusPending:   "待维修",
	AnomalyStatusRepairing: "维修中",
	AnomalyStatusCompleted: "已完成",
}

// 维修状态枚举
type RepairStatus string

const (
	RepairStatusInProgress RepairStatus = "in_progress" // 进行中
	RepairStatusCompleted  RepairStatus = "completed"   // 已完成
)

// 维修状态中文显示映射
var RepairStatusDisplayNames = map[RepairStatus]string{
	RepairStatusInProgress: "进行中",
	RepairStatusCompleted:  "已完成",
}

// 工具函数：获取中文显示名称
func (r UserRole) DisplayName() string {
	if name, ok := UserRoleDisplayNames[r]; ok {
		return name
	}
	return string(r)
}

// 工具函数：获取角色ID
func (r UserRole) ID() int {
	if id, ok := UserRoleIDs[r]; ok {
		return id
	}
	return 0
}

func (s MachineStatus) DisplayName() string {
	if name, ok := MachineStatusDisplayNames[s]; ok {
		return name
	}
	return string(s)
}

func (s AnomalySeverity) DisplayName() string {
	if name, ok := AnomalySeverityDisplayNames[s]; ok {
		return name
	}
	return string(s)
}

func (s AnomalyStatus) DisplayName() string {
	if name, ok := AnomalyStatusDisplayNames[s]; ok {
		return name
	}
	return string(s)
}

func (s RepairStatus) DisplayName() string {
	if name, ok := RepairStatusDisplayNames[s]; ok {
		return name
	}
	return string(s)
}

// 工具函数：从中文名称获取枚举值
func ParseUserRole(displayName string) UserRole {
	for role, name := range UserRoleDisplayNames {
		if name == displayName {
			return role
		}
	}
	return ""
}

func ParseMachineStatus(displayName string) MachineStatus {
	for status, name := range MachineStatusDisplayNames {
		if name == displayName {
			return status
		}
	}
	return ""
}

func ParseAnomalySeverity(displayName string) AnomalySeverity {
	for severity, name := range AnomalySeverityDisplayNames {
		if name == displayName {
			return severity
		}
	}
	return ""
}

func ParseAnomalyStatus(displayName string) AnomalyStatus {
	for status, name := range AnomalyStatusDisplayNames {
		if name == displayName {
			return status
		}
	}
	return ""
}

func ParseRepairStatus(displayName string) RepairStatus {
	for status, name := range RepairStatusDisplayNames {
		if name == displayName {
			return status
		}
	}
	return ""
}