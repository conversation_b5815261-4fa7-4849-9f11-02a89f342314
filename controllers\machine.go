package controllers

import (
	"strconv"
	"textile-factory-backend/config"
	"textile-factory-backend/models"
	"textile-factory-backend/response"
	"github.com/gin-gonic/gin"
)

func GetMachineList(c *gin.Context) {
	var machines []models.Machine
	
	page, _ := strconv.Atoi(c<PERSON>("page", "1"))
	pageSize, _ := strconv.Atoi(c<PERSON>("page_size", "10"))
	status := c.Query("status")
	
	query := config.DB.Model(&models.Machine{})
	
	if status != "" {
		query = query.Where("alarm_type = ?", status)
	}
	
	var total int64
	query.Count(&total)
	
	offset := (page - 1) * pageSize
	result := query.Offset(offset).Limit(pageSize).Find(&machines)
	
	if result.Error != nil {
		response.InternalServerError(c, "查询机器列表失败")
		return
	}
	
	// 获取每台机器的异常数量
	for i := range machines {
		var anomalyCount int64
		config.DB.Model(&models.Anomaly{}).Where("machine_code = ? AND alarm_type != '已完成'", machines[i].MachineCode).Count(&anomalyCount)
		machines[i].AlarmRemark = strconv.FormatInt(anomalyCount, 10) // 临时使用AlarmRemark字段存储异常数量
	}
	
	response.Pagination(c, machines, total, page, pageSize)
}

func GetMachineByCode(c *gin.Context) {
	code := c.Param("code")
	
	var machine models.Machine
	result := config.DB.Where("machine_code = ?", code).First(&machine)
	
	if result.Error != nil {
		response.NotFound(c, "机器不存在")
		return
	}
	
	// 获取机器的异常记录
	var anomalies []models.Anomaly
	config.DB.Where("machine_code = ?", machine.MachineCode).
		Order("state_time DESC").
		Find(&anomalies)
	
	response.Success(c, gin.H{
		"machine":   machine,
		"anomalies": anomalies,
	})
}

func GetMachineByQRCode(c *gin.Context) {
	qrCode := c.Param("qr_code")
	
	var machine models.Machine
	result := config.DB.Where("machine_code = ?", qrCode).First(&machine)
	
	if result.Error != nil {
		response.NotFound(c, "机器不存在")
		return
	}
	
	response.Success(c, machine)
}

func UpdateMachineStatus(c *gin.Context) {
	id := c.Param("id")
	
	var req struct {
		AlarmType string `json:"alarm_type" binding:"required"`
	}
	
	if err := c.ShouldBindJSON(&req); err != nil {
		response.BadRequest(c, err.Error())
		return
	}
	
	result := config.DB.Model(&models.Machine{}).Where("machine_id = ?", id).Update("alarm_type", req.AlarmType)
	
	if result.Error != nil {
		response.InternalServerError(c, "更新机器状态失败")
		return
	}
	
	if result.RowsAffected == 0 {
		response.NotFound(c, "机器不存在")
		return
	}
	
	response.SuccessWithMessage(c, "机器状态更新成功", nil)
}