package models

import (
	"time"
	"textile-factory-backend/constants"
	"gorm.io/gorm"
)

// WorkerInfo represents the workerInfo table
type WorkerInfo struct {
	WorkerID     uint      `json:"worker_id" gorm:"primaryKey;column:WorkerID"`
	Dept         string    `json:"dept" gorm:"column:Dept;type:nvarchar(50);default:''"`
	WorkerCode   string    `json:"worker_code" gorm:"column:WorkerCode;type:nvarchar(50);default:''"`
	WorkerName   string    `json:"worker_name" gorm:"column:WorkerName;type:nvarchar(50);default:''"`
	Password     string    `json:"-" gorm:"column:Password;type:nvarchar(100);default:''"`  // 新增密码字段
	Sex          string    `json:"sex" gorm:"column:Sex;type:nvarchar(10);default:''"`
	IdCard       string    `json:"id_card" gorm:"column:IdCard;type:nvarchar(50);default:''"`
	WorkClass    string    `json:"work_class" gorm:"column:WorkClass;type:nvarchar(50);default:''"`
	InWorkTime   *time.Time `json:"in_work_time" gorm:"column:InWorkTime;type:datetime;default:getdate()"`
	ContractTime *time.Time `json:"contract_time" gorm:"column:ContractTime;type:datetime;default:getdate()"`
	Education    string    `json:"education" gorm:"column:Education;type:nvarchar(50);default:''"`
	HomeAddress  string    `json:"home_address" gorm:"column:HomeAdress;type:nvarchar(100);default:''"`
	HomeTel      string    `json:"home_tel" gorm:"column:HomeTel;type:nvarchar(50);default:''"`
	WorkCard     string    `json:"work_card" gorm:"column:WorkCard;type:nvarchar(50);default:''"`
	UnFlag       bool      `json:"un_flag" gorm:"column:UnFlag;type:bit;default:false"`
	HeadPhoto    []byte    `json:"head_photo" gorm:"column:HeadPhoto;type:image"`
	UpdateDtm    time.Time `json:"update_dtm" gorm:"column:UpdateDtm;type:datetime;default:getdate()"`
	UpdateBy     string    `json:"update_by" gorm:"column:UpdateBy;type:nvarchar(50);default:''"`
	CODER16      string    `json:"coder16" gorm:"column:CODER16;type:nvarchar(30)"`
	WorkerType   int       `json:"worker_type" gorm:"column:WorkerType;type:int;default:0"`
	BasicWage    float64   `json:"basic_wage" gorm:"column:BasicWage;type:decimal(18,2);default:0"`
	DeptID       uint      `json:"dept_id" gorm:"column:DeptID;type:decimal(18);default:0"`
	OutWorkTime  *time.Time `json:"out_work_time" gorm:"column:OutWorkTime;type:datetime"`
	TeamGroup    string    `json:"team_group" gorm:"column:TeamGroup;type:nvarchar(50);default:''"`
	TeamLeader   string    `json:"team_leader" gorm:"column:TeamLeader;type:nvarchar(50);default:''"`
}

// 指定表名
func (WorkerInfo) TableName() string {
	return "WorkerInfo"
}

// User alias for backward compatibility
type User = WorkerInfo

type MachineRunningInfo struct {
	MachineId      uint      `json:"machine_id" gorm:"primaryKey;column:MachineId;type:decimal(18)"`
	MachineCode    string    `json:"machine_code" gorm:"column:MachineCode;type:nvarchar(30);default:''"`
	MachineName    string    `json:"machine_name" gorm:"column:MachineName;type:nvarchar(30);default:''"`
	MachineType    string    `json:"machine_type" gorm:"column:MachineType;type:nvarchar(50);default:''"`
	FSit           string    `json:"f_sit" gorm:"column:FSit;type:nvarchar(50);default:''"`
	MSit           string    `json:"m_sit" gorm:"column:MSit;type:nvarchar(50);default:''"`
	Size           string    `json:"size" gorm:"column:Size;type:nvarchar(10);default:''"`
	Needle         string    `json:"needle" gorm:"column:Needle;type:nvarchar(10);default:''"`
	NeedleTotal    string    `json:"needle_total" gorm:"column:NeedleTotal;type:nvarchar(20);default:''"`
	ClothType      string    `json:"cloth_type" gorm:"column:ClothType;type:nvarchar(300);default:''"`
	WorkerName     string    `json:"worker_name" gorm:"column:WorkerName;type:nvarchar(50);default:''"`
	Rxv            float64   `json:"rxv" gorm:"column:Rxv;type:decimal(18,3);default:0"`
	Js             float64   `json:"js" gorm:"column:Js;type:decimal(18,2);default:0"`
	RJs            float64   `json:"r_js" gorm:"column:RJs;type:decimal(18,2);default:0"`
	NeedJs         float64   `json:"need_js" gorm:"column:NeedJs;type:decimal(18,2);default:0"`
	Cc             int       `json:"cc" gorm:"column:Cc;type:int;default:0"`
	F              string    `json:"f" gorm:"column:F;type:nvarchar(50);default:''"`
	St             time.Time `json:"st" gorm:"column:St;type:datetime;default:'1970-01-01'"`
	RxvDisplay     string    `json:"rxv_display" gorm:"column:RxvDisplay;type:nvarchar(200);default:''"`
	Coder16        string    `json:"coder16" gorm:"column:Coder16;type:nvarchar(200);default:''"`
	WorkerType     int       `json:"worker_type" gorm:"column:WorkerType;type:int;default:0"`
	DayChar        string    `json:"day_char" gorm:"column:DayChar;type:nvarchar(200);default:''"`
	Amount         float64   `json:"amount" gorm:"column:Amount;type:decimal(18,2);default:0"`
	HaveAlarm      bool      `json:"have_alarm" gorm:"column:HaveAlarm;type:bit;default:0"`
	AlarmType      string    `json:"alarm_type" gorm:"column:AlarmType;type:nvarchar(20);default:''"`
	AlarmRemark    string    `json:"alarm_remark" gorm:"column:AlarmRemark;type:nvarchar(200);default:''"`
	StopCount      int       `json:"stop_count" gorm:"column:StopCount;type:int;default:0"`
	RunTimes       int       `json:"run_times" gorm:"column:RunTimes;type:int;default:0"`
	StopTimes      int       `json:"stop_times" gorm:"column:StopTimes;type:int;default:0"`
	CutClothQty    float64   `json:"cut_cloth_qty" gorm:"column:CutClothQty;type:decimal(18,2);default:0"`
	WeightTtl      float64   `json:"weight_ttl" gorm:"column:WeightTtl;type:decimal(18,2);default:0"`
	OrderCode      string    `json:"order_code" gorm:"column:OrderCode;type:nvarchar(50);default:''"`
	PlanQty        float64   `json:"plan_qty" gorm:"column:PlanQty;type:decimal(18,2);default:0"`
	TotalCount     int       `json:"total_count" gorm:"column:TotalCount;type:int;default:0"`
	CutClothNum    int       `json:"cut_cloth_num" gorm:"column:CutClothNum;type:int;default:0"`
	SingleWeight   float64   `json:"single_weight" gorm:"column:SingleWeight;type:decimal(18,2);default:0"`
	RealQty        float64   `json:"real_qty" gorm:"column:RealQty;type:decimal(18,2);default:0"`
	PlanPiece      float64   `json:"plan_piece" gorm:"column:PlanPiece;type:decimal(18,2);default:0"`
	RealPiece      float64   `json:"real_piece" gorm:"column:RealPiece;type:decimal(18,2);default:0"`
	UpdateTime     time.Time `json:"update_time" gorm:"column:UpdateTime;type:datetime;default:getdate()"`
	Brand          string    `json:"brand" gorm:"column:Brand;type:nvarchar(50);default:''"`
	MachineCategory string   `json:"machine_category" gorm:"column:MachineCategory;type:nvarchar(50);default:''"`
	RoadNums       string    `json:"road_nums" gorm:"column:RoadNums;type:nvarchar(50);default:''"`
	Ct             time.Time `json:"ct" gorm:"column:Ct;type:datetime;default:getdate()"`
	RepairItemCode string    `json:"repair_item_code" gorm:"column:RepairItemCode;type:nvarchar(100);default:''"`
	RepairItemName string    `json:"repair_item_name" gorm:"column:RepairItemName;type:nvarchar(100);default:''"`
}

// 指定表名
func (MachineRunningInfo) TableName() string {
	return "MachineRunningInfo"
}

// Machine alias for backward compatibility
type Machine = MachineRunningInfo

type MachineStateAlarm struct {
	MachineStateAlarmID uint      `json:"machine_state_alarm_id" gorm:"primaryKey;column:MachineStateAlarmID;type:decimal(18);autoIncrement"`
	MachineCode         string    `json:"machine_code" gorm:"column:MachineCode;type:nvarchar(30);not null"`
	Machine             string    `json:"machine" gorm:"column:Machine;type:nvarchar(30);not null"`
	StateM              string    `json:"state_m" gorm:"column:StateM;type:nvarchar(50);not null"`
	StateA              string    `json:"state_a" gorm:"column:StateA;type:nvarchar(50);not null"`
	StateTime           time.Time `json:"state_time" gorm:"column:StateTime;type:datetime;not null"`
	EndTime             time.Time `json:"end_time" gorm:"column:EndTime;type:datetime;not null"`
	AlarmRmark          string    `json:"alarm_rmark" gorm:"column:AlarmRmark;type:nvarchar(200);not null"`
	AlarmType           string    `json:"alarm_type" gorm:"column:AlarmType;type:nvarchar(50);not null"`
	UpdateDtm           time.Time `json:"update_dtm" gorm:"column:UpdateDtm;type:datetime;not null"`
	UpdateBy            string    `json:"update_by" gorm:"column:UpdateBy;type:nvarchar(30);not null"`
	SPT                 int       `json:"spt" gorm:"column:SPT;type:int;not null"`
	F                   string    `json:"f" gorm:"column:F;type:nvarchar(50);not null"`
	D                   string    `json:"d" gorm:"column:D;type:nvarchar(30);not null"`
	DayChar             string    `json:"day_char" gorm:"column:DayChar;type:nvarchar(50);not null"`
	WorkerName          string    `json:"worker_name" gorm:"column:WorkerName;type:nvarchar(50);not null"`
	MSit                string    `json:"m_sit" gorm:"column:MSit;type:nvarchar(50);not null"`
	FSit                string    `json:"f_sit" gorm:"column:FSit;type:nvarchar(50);not null"`
	SPTStr              string    `json:"spt_str" gorm:"column:SPTStr;type:nvarchar(40);default:'0';not null"`
}

// 指定表名
func (MachineStateAlarm) TableName() string {
	return "MachineStateAlarm"
}

// Anomaly alias for backward compatibility
type Anomaly = MachineStateAlarm

type Repair struct {
	ID         uint                   `json:"id" gorm:"primaryKey"`
	AnomalyID  uint                   `json:"anomaly_id" gorm:"not null;uniqueIndex"`
	MechanicID uint                   `json:"mechanic_id" gorm:"not null"`
	StartTime  time.Time              `json:"start_time"`
	EndTime    *time.Time             `json:"end_time"`
	Process    string                 `json:"process"`
	Parts      string                 `json:"parts"`
	Duration   int                    `json:"duration"`
	Status     constants.RepairStatus `json:"status" gorm:"default:'in_progress';type:nvarchar(20)"`
	StatusName string                 `json:"status_name" gorm:"not null"`
	CreatedAt  time.Time              `json:"created_at"`
	UpdatedAt  time.Time              `json:"updated_at"`

	Anomaly  Anomaly `json:"anomaly" gorm:"foreignKey:AnomalyID;references:MachineStateAlarmID"`
	Mechanic User    `json:"mechanic" gorm:"foreignKey:MechanicID"`
}

type RefreshToken struct {
	ID        uint      `json:"id" gorm:"primaryKey"`
	UserID    uint      `json:"user_id" gorm:"not null"`
	Token     string    `json:"token" gorm:"uniqueIndex;not null"`
	ExpiresAt time.Time `json:"expires_at" gorm:"not null"`
	CreatedAt time.Time `json:"created_at"`
	UpdatedAt time.Time `json:"updated_at"`

	User User `json:"user" gorm:"foreignKey:UserID"`
}

// GORM Hooks - 自动填充显示名称字段
func (r *Repair) BeforeCreate(tx *gorm.DB) error {
	r.StatusName = r.Status.DisplayName()
	return nil
}

func (r *Repair) BeforeUpdate(tx *gorm.DB) error {
	if tx.Statement.Changed("status") {
		r.StatusName = r.Status.DisplayName()
	}
	return nil
}
